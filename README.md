# Adaptive AI Tic-Tac-Toe Web Game

A web-based implementation of Tic-Tac-Toe with an adaptive AI that learns from gameplay using Q-learning reinforcement learning.

## Features

- **Adaptive AI**: The AI learns and improves its strategy over time using Q-learning
- **Persistent Learning**: AI knowledge is saved in browser's localStorage
- **Training Mode**: Quick training option to run 10,000 games instantly
- **Game Statistics**: Track wins, losses, and draws
- **Responsive Design**: Works on desktop and mobile devices
- **Clean Interface**: Modern, intuitive user interface

## How to Run

1. **Simple Method**: Just open `index.html` in any modern web browser
2. **Local Server** (recommended for best performance):
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js (if you have http-server installed)
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```
   Then open `http://localhost:8000` in your browser

## How to Play

1. **You are O, AI is X**
2. Click on any empty cell to make your move
3. The AI will automatically make its move after yours
4. Try to get three in a row (horizontally, vertically, or diagonally)
5. The AI learns from each game and gets progressively better

## Controls

- **New Game**: Start a fresh game
- **Train AI**: Run 10,000 training games to quickly improve the AI
- **Reset AI**: Clear all AI knowledge and start from scratch

## Technical Details

### Q-Learning Implementation

The AI uses Q-learning with the following parameters:
- **Learning Rate**: 0.1
- **Discount Factor**: 0.9
- **Exploration Rate (Epsilon)**: 0.1
- **Training Episodes**: 10,000 per training session

### Game States

- Each board state is represented as a 9-character string
- Q-values are stored for state-action pairs
- The AI chooses actions based on the highest Q-value for the current state

### Data Persistence

- Q-table is saved to browser's localStorage
- Game statistics are also persisted
- Data survives browser restarts

## File Structure

```
├── index.html      # Main HTML file
├── style.css       # Styling and responsive design
├── script.js       # Game logic and AI implementation
└── README.md       # This file
```

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge
- Any modern browser with ES6+ support

## Differences from Original Python Version

1. **Web-based**: Runs in browser instead of desktop application
2. **Persistent Storage**: Uses localStorage instead of pickle files
3. **Async Training**: Training runs asynchronously with progress indication
4. **Enhanced UI**: More interactive and responsive interface
5. **Statistics Tracking**: Added game statistics and win/loss tracking

## Tips for Best AI Performance

1. **Initial Training**: Click "Train AI" when you first load the game
2. **Let it Learn**: Play several games to let the AI adapt to your strategy
3. **Reset Strategically**: Only reset the AI if you want to start the learning process over

## Future Enhancements

- Difficulty levels
- Different AI algorithms
- Multiplayer mode
- Game replay functionality
- Export/import AI knowledge

Enjoy playing against the adaptive AI! 🎮🤖
