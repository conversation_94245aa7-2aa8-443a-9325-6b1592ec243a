// Global Q-Table and learning parameters
let Q = {};
const LEARNING_RATE = 0.1;
const DISCOUNT = 0.9;
const EPSILON = 0.1;
const TRAINING_EPISODES = 10000;

// Game statistics
let gameStats = {
    gamesPlayed: 0,
    aiWins: 0,
    playerWins: 0,
    draws: 0
};

// Game logic class
class TicTacToe {
    constructor() {
        this.board = Array(9).fill(' ');
        this.winner = null;
    }

    availableMoves() {
        return this.board.map((spot, index) => spot === ' ' ? index : null).filter(val => val !== null);
    }

    makeMove(index, player) {
        if (this.board[index] === ' ') {
            this.board[index] = player;
            this.checkWinner(player);
            return true;
        }
        return false;
    }

    check<PERSON>inner(player) {
        const winConditions = [
            [0,1,2], [3,4,5], [6,7,8], // rows
            [0,3,6], [1,4,7], [2,5,8], // columns
            [0,4,8], [2,4,6] // diagonals
        ];
        
        for (let combo of winConditions) {
            if (combo.every(index => this.board[index] === player)) {
                this.winner = player;
                return true;
            }
        }
        return false;
    }

    isFull() {
        return !this.board.includes(' ');
    }

    reset() {
        this.board = Array(9).fill(' ');
        this.winner = null;
    }

    getState() {
        return this.board.join('');
    }
}

// Q-learning training function
async function trainAI() {
    const trainButton = document.getElementById('trainAI');
    const progressContainer = document.getElementById('progressContainer');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const trainingStatus = document.getElementById('trainingStatus');
    
    trainButton.disabled = true;
    progressContainer.style.display = 'block';
    trainingStatus.textContent = 'Training AI... Please wait.';
    
    const game = new TicTacToe();
    
    for (let episode = 0; episode < TRAINING_EPISODES; episode++) {
        game.reset();
        let state = game.getState();
        let player = 'X';
        let done = false;

        while (!done) {
            const moves = game.availableMoves();
            let action;
            
            if (Math.random() < EPSILON) {
                action = moves[Math.floor(Math.random() * moves.length)];
            } else {
                const qVals = moves.map(move => Q[`${state},${move}`] || 0);
                const maxQ = Math.max(...qVals);
                const bestMoves = moves.filter((move, i) => qVals[i] === maxQ);
                action = bestMoves[Math.floor(Math.random() * bestMoves.length)];
            }

            game.makeMove(action, player);
            const newState = game.getState();

            let reward = 0;
            if (game.winner === player) {
                reward = 1;
                done = true;
            } else if (game.isFull()) {
                reward = 0.5;
                done = true;
            }

            if (!done) {
                const oppMoves = game.availableMoves();
                if (oppMoves.length > 0) {
                    const oppMove = oppMoves[Math.floor(Math.random() * oppMoves.length)];
                    game.makeMove(oppMove, 'O');
                    if (game.winner === 'O') {
                        reward = -1;
                        done = true;
                    } else if (game.isFull()) {
                        reward = 0.5;
                        done = true;
                    }
                }
            }

            const oldQ = Q[`${state},${action}`] || 0;
            const futureQs = game.availableMoves().map(move => Q[`${newState},${move}`] || 0);
            const futureQ = futureQs.length > 0 ? Math.max(...futureQs) : 0;
            Q[`${state},${action}`] = oldQ + LEARNING_RATE * (reward + DISCOUNT * futureQ - oldQ);
            
            state = newState;
        }

        // Update progress every 100 episodes
        if (episode % 100 === 0) {
            const progress = (episode / TRAINING_EPISODES) * 100;
            progressFill.style.width = `${progress}%`;
            progressText.textContent = `${Math.round(progress)}%`;
            
            // Allow UI to update
            await new Promise(resolve => setTimeout(resolve, 1));
        }
    }
    
    // Complete progress
    progressFill.style.width = '100%';
    progressText.textContent = '100%';
    
    // Save Q-table to localStorage
    localStorage.setItem('ticTacToeQ', JSON.stringify(Q));
    
    setTimeout(() => {
        trainButton.disabled = false;
        progressContainer.style.display = 'none';
        trainingStatus.textContent = `Training complete! AI trained on ${TRAINING_EPISODES} games.`;
        setTimeout(() => {
            trainingStatus.textContent = '';
        }, 3000);
    }, 500);
}

// Game GUI class
class TicTacToeGUI {
    constructor() {
        this.game = new TicTacToe();
        this.isPlayerTurn = true;
        this.gameActive = true;
        
        this.initializeElements();
        this.loadQTable();
        this.loadGameStats();
        this.setupEventListeners();
        this.updateUI();
    }

    initializeElements() {
        this.statusElement = document.getElementById('status');
        this.boardElement = document.getElementById('gameBoard');
        this.cells = document.querySelectorAll('.cell');
        this.newGameButton = document.getElementById('newGame');
        this.trainButton = document.getElementById('trainAI');
        this.resetButton = document.getElementById('resetAI');
    }

    loadQTable() {
        const savedQ = localStorage.getItem('ticTacToeQ');
        if (savedQ) {
            Q = JSON.parse(savedQ);
        }
    }

    loadGameStats() {
        const savedStats = localStorage.getItem('ticTacToeStats');
        if (savedStats) {
            gameStats = JSON.parse(savedStats);
            this.updateStats();
        }
    }

    saveGameStats() {
        localStorage.setItem('ticTacToeStats', JSON.stringify(gameStats));
    }

    setupEventListeners() {
        this.cells.forEach((cell, index) => {
            cell.addEventListener('click', () => this.handleCellClick(index));
        });

        this.newGameButton.addEventListener('click', () => this.newGame());
        this.trainButton.addEventListener('click', () => trainAI());
        this.resetButton.addEventListener('click', () => this.resetAI());
    }

    handleCellClick(index) {
        if (!this.gameActive || !this.isPlayerTurn || this.game.board[index] !== ' ') {
            return;
        }

        this.makePlayerMove(index);
    }

    makePlayerMove(index) {
        this.game.makeMove(index, 'O');
        this.updateUI();
        
        if (this.game.winner === 'O') {
            this.endGame('You win! 🎉');
            gameStats.playerWins++;
            gameStats.gamesPlayed++;
            this.saveGameStats();
            this.updateStats();
            return;
        }
        
        if (this.game.isFull()) {
            this.endGame("It's a draw! 🤝");
            gameStats.draws++;
            gameStats.gamesPlayed++;
            this.saveGameStats();
            this.updateStats();
            return;
        }

        this.isPlayerTurn = false;
        this.statusElement.textContent = "AI is thinking...";
        
        setTimeout(() => this.makeAIMove(), 500);
    }

    makeAIMove() {
        const state = this.game.getState();
        const moves = this.game.availableMoves();
        
        if (moves.length === 0) return;
        
        const qVals = moves.map(move => Q[`${state},${move}`] || 0);
        const maxQ = Math.max(...qVals);
        const bestMoves = moves.filter((move, i) => qVals[i] === maxQ);
        const bestMove = bestMoves[Math.floor(Math.random() * bestMoves.length)];
        
        this.game.makeMove(bestMove, 'X');
        this.updateUI();
        
        if (this.game.winner === 'X') {
            this.endGame('AI wins! 🤖');
            gameStats.aiWins++;
            gameStats.gamesPlayed++;
            this.saveGameStats();
            this.updateStats();
            return;
        }
        
        if (this.game.isFull()) {
            this.endGame("It's a draw! 🤝");
            gameStats.draws++;
            gameStats.gamesPlayed++;
            this.saveGameStats();
            this.updateStats();
            return;
        }

        this.isPlayerTurn = true;
        this.statusElement.textContent = "Your turn (O)";
    }

    updateUI() {
        this.cells.forEach((cell, index) => {
            const value = this.game.board[index];
            cell.textContent = value === ' ' ? '' : value;
            cell.className = 'cell';
            
            if (value === 'X') {
                cell.classList.add('x');
            } else if (value === 'O') {
                cell.classList.add('o');
            }
            
            if (!this.gameActive || value !== ' ') {
                cell.classList.add('disabled');
            }
        });
    }

    updateStats() {
        document.getElementById('gamesPlayed').textContent = gameStats.gamesPlayed;
        document.getElementById('aiWins').textContent = gameStats.aiWins;
        document.getElementById('playerWins').textContent = gameStats.playerWins;
        document.getElementById('draws').textContent = gameStats.draws;
    }

    endGame(message) {
        this.gameActive = false;
        this.statusElement.textContent = message;
        this.cells.forEach(cell => cell.classList.add('disabled'));
    }

    newGame() {
        this.game.reset();
        this.isPlayerTurn = true;
        this.gameActive = true;
        this.statusElement.textContent = "Your turn (O)";
        this.updateUI();
    }

    resetAI() {
        if (confirm('Are you sure you want to reset the AI? This will clear all learned knowledge.')) {
            Q = {};
            localStorage.removeItem('ticTacToeQ');
            gameStats = { gamesPlayed: 0, aiWins: 0, playerWins: 0, draws: 0 };
            localStorage.removeItem('ticTacToeStats');
            this.updateStats();
            alert('AI has been reset!');
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new TicTacToeGUI();
});
