* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
    text-align: center;
}

h1 {
    color: #333;
    margin-bottom: 20px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    margin-bottom: 30px;
}

.status {
    font-size: 1.5em;
    font-weight: bold;
    color: #555;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 30px auto;
    max-width: 300px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 15px;
    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cell {
    aspect-ratio: 1;
    background: white;
    border: 3px solid #e9ecef;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.cell:hover {
    background: #f8f9fa;
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.cell.x {
    color: #e74c3c;
    background: #ffeaea;
    border-color: #e74c3c;
}

.cell.o {
    color: #3498db;
    background: #eaf4ff;
    border-color: #3498db;
}

.cell.disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.training-info {
    margin: 20px 0;
}

.training-status {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 10px;
}

.progress-container {
    margin: 15px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    margin-top: 5px;
    font-weight: bold;
    color: #555;
}

.game-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin: 25px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px solid #e9ecef;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 5px;
}

.stat span:last-child {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
}

.instructions {
    margin-top: 30px;
    text-align: left;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid #e9ecef;
}

.instructions h3 {
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.instructions ul {
    list-style-position: inside;
    color: #555;
    line-height: 1.6;
}

.instructions li {
    margin-bottom: 8px;
}

@media (max-width: 480px) {
    .container {
        padding: 20px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .game-board {
        max-width: 250px;
    }
    
    .cell {
        font-size: 2em;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
    
    .game-stats {
        grid-template-columns: 1fr;
    }
}
