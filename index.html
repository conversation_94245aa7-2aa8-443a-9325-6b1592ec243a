<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptive AI Tic-Tac-Toe</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Adaptive AI Tic-Tac-Toe</h1>
        <div class="game-info">
            <div class="status" id="status">Your turn (O)</div>
            <div class="controls">
                <button id="newGame" class="btn">New Game</button>
                <button id="trainAI" class="btn">Train AI</button>
                <button id="resetAI" class="btn">Reset AI</button>
            </div>
        </div>
        
        <div class="game-board" id="gameBoard">
            <div class="cell" data-index="0"></div>
            <div class="cell" data-index="1"></div>
            <div class="cell" data-index="2"></div>
            <div class="cell" data-index="3"></div>
            <div class="cell" data-index="4"></div>
            <div class="cell" data-index="5"></div>
            <div class="cell" data-index="6"></div>
            <div class="cell" data-index="7"></div>
            <div class="cell" data-index="8"></div>
        </div>

        <div class="training-info">
            <div id="trainingStatus" class="training-status"></div>
            <div class="progress-container" id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">0%</div>
            </div>
        </div>

        <div class="game-stats">
            <div class="stat">
                <span class="stat-label">Games Played:</span>
                <span id="gamesPlayed">0</span>
            </div>
            <div class="stat">
                <span class="stat-label">AI Wins:</span>
                <span id="aiWins">0</span>
            </div>
            <div class="stat">
                <span class="stat-label">Player Wins:</span>
                <span id="playerWins">0</span>
            </div>
            <div class="stat">
                <span class="stat-label">Draws:</span>
                <span id="draws">0</span>
            </div>
        </div>

        <div class="instructions">
            <h3>How to Play:</h3>
            <ul>
                <li>You are <strong>O</strong>, AI is <strong>X</strong></li>
                <li>Click on any empty cell to make your move</li>
                <li>The AI will learn from each game and get better over time</li>
                <li>Use "Train AI" to run 10,000 training games quickly</li>
                <li>Use "Reset AI" to clear the AI's memory and start fresh</li>
            </ul>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
